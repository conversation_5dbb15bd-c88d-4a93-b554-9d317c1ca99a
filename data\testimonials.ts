export interface Testimonial {
   name: string;
   initials: string;
   service: string;
   rating: number;
   text: string;
}

export const testimonials: Testimonial[] = [
   {
      name: "<PERSON>",
      initials: "<PERSON><PERSON><PERSON>",
      service: "Wedding Photography",
      rating: 5,
      text: "Astral Studios captured our wedding day perfectly! The photos are absolutely stunning and we couldn't be happier with the results. Professional, creative, and so easy to work with.",
   },
   {
      name: "<PERSON>",
      initials: "E&T",
      service: "Pregnancy Photography",
      rating: 5,
      text: "The maternity shoot was such a wonderful experience. The photographer made me feel so comfortable and the photos are beautiful. I'll treasure these memories forever.",
   },
   {
      name: "<PERSON> & <PERSON>",
      initials: "M<PERSON><PERSON>",
      service: "Pre-Wedding Shoot",
      rating: 5,
      text: "Our engagement photos exceeded all expectations! The creativity and attention to detail was incredible. We can't wait to work with them again for our wedding.",
   },
];

// Additional testimonials for different sections
export const shortTestimonials = [
   {
      name: "<PERSON>",
      text: "Absolutely phenomenal work! Every photo tells a story.",
      service: "Wedding Photography",
   },
   {
      name: "<PERSON>",
      text: "Professional, creative, and captured every precious moment.",
      service: "Child Dedication",
   },
   {
      name: "<PERSON> & Jennifer",
      text: "The 360 booth was the highlight of our reception!",
      service: "360 Video Booth",
   },
];
