"use client";

import GalleryUpload from "@/components/gallery-upload";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";

interface UploadImagesDialogProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   onUploadComplete?: () => void;
   title?: string;
   subtitle?: string;
}

export default function UploadImagesDialog({
   open,
   onOpenChange,
   onUploadComplete,
   title = "Upload Images",
   subtitle = "Add new images to your gallery",
}: UploadImagesDialogProps) {
   const handleUploadComplete = () => {
      if (onUploadComplete) {
         onUploadComplete();
      }
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-4xl">
            <DialogHeader>
               <DialogTitle>{title}</DialogTitle>
               <DialogDescription>{subtitle}</DialogDescription>
            </DialogHeader>

            <div className="mt-4">
               <GalleryUpload
                  options={{
                     albumId: null, // No album ID for ungrouped images
                     maxFiles: 20,
                     maxSizeBytes: 50 * 1024 * 1024, // 50MB
                  }}
                  onUploadComplete={handleUploadComplete}
                  title="Upload Images"
                  subtitle="Drag and drop your images here, or click to browse"
               />
            </div>
         </DialogContent>
      </Dialog>
   );
}
