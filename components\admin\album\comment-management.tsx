"use client";

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
   useAdminAlbumCommentCount,
   useAdminAlbumComments,
   usePermanentlyDeleteComment,
   useRemoveAdminResponse,
   useRestoreComment,
   useSoftDeleteComment,
   useUpdateAdminResponse,
} from "@/lib/hooks/use-admin-comments";
import { CommentWithReplies } from "@/lib/models";
import { cn } from "@/lib/utils";
import {
   ArrowUturnLeftIcon,
   ChatBubbleLeftRightIcon,
   ChatBubbleOvalLeftEllipsisIcon,
   PaperAirplaneIcon,
   PencilSquareIcon,
   TrashIcon,
} from "@heroicons/react/24/solid";
import { AlertTriangle, MoreVertical, Reply, Search, X } from "lucide-react";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import Avatar from "../../ui/avatar";

interface AdminCommentManagementProps {
   albumId: string;
   albumName: string;
}

export default function AdminCommentManagement({
   albumId,
   albumName,
}: AdminCommentManagementProps) {
   const [activeTab, setActiveTab] = useState("all");
   const [searchQuery, setSearchQuery] = useState("");
   const [showMore, setShowMore] = useState(false);
   const [editingResponse, setEditingResponse] = useState<string | null>(null);
   const [responseText, setResponseText] = useState("");
   const [deleteDialog, setDeleteDialog] = useState<{
      open: boolean;
      commentId: string;
      permanent: boolean;
   }>({ open: false, commentId: "", permanent: false });

   // Determine filters based on active tab and search
   const filters = useMemo(
      () => ({
         showDeleted: activeTab === "deleted",
         searchQuery: searchQuery.trim() || undefined,
         limit: showMore ? 50 : 2,
         offset: 0,
      }),
      [activeTab, searchQuery, showMore]
   );

   // Fetch comments and counts
   const { data: commentsResponse, isLoading: commentsLoading } =
      useAdminAlbumComments(albumId, filters);
   const { data: allCountResponse } = useAdminAlbumCommentCount(albumId, {
      showDeleted: false,
   });
   const { data: deletedCountResponse } = useAdminAlbumCommentCount(albumId, {
      showDeleted: true,
   });

   // Mutations
   const softDeleteMutation = useSoftDeleteComment();
   const restoreMutation = useRestoreComment();
   const updateResponseMutation = useUpdateAdminResponse();
   const removeResponseMutation = useRemoveAdminResponse();
   const permanentDeleteMutation = usePermanentlyDeleteComment();

   const comments = commentsResponse?.data as CommentWithReplies[] | undefined;
   const allCount = allCountResponse?.data as number | 0;
   const deletedCount = deletedCountResponse?.data as number | 0;

   const handleSoftDelete = (commentId: string) => {
      setDeleteDialog({ open: true, commentId, permanent: false });
   };

   const handlePermanentDelete = (commentId: string) => {
      setDeleteDialog({ open: true, commentId, permanent: true });
   };

   const handleRestore = (commentId: string) => {
      restoreMutation.mutate(commentId);
   };

   const handleStartEditResponse = (
      commentId: string,
      currentResponse?: string
   ) => {
      setEditingResponse(commentId);
      setResponseText(currentResponse || "");
   };

   const handleSaveResponse = (commentId: string) => {
      if (!responseText.trim()) {
         toast.error("Admin response cannot be empty");
         return;
      }

      updateResponseMutation.mutate(
         { commentId, input: { adminResponse: responseText.trim() } },
         {
            onSuccess: () => {
               setEditingResponse(null);
               setResponseText("");
            },
         }
      );
   };

   const handleRemoveResponse = (commentId: string) => {
      removeResponseMutation.mutate(commentId);
   };

   const handleCancelEdit = () => {
      setEditingResponse(null);
      setResponseText("");
   };

   const confirmDelete = () => {
      if (deleteDialog.permanent) {
         permanentDeleteMutation.mutate(deleteDialog.commentId);
      } else {
         softDeleteMutation.mutate(deleteDialog.commentId);
      }
      setDeleteDialog({ open: false, commentId: "", permanent: false });
   };

   const formatDate = (date: Date | string) => {
      return new Date(date).toLocaleDateString("en-US", {
         year: "numeric",
         month: "short",
         day: "numeric",
         hour: "2-digit",
         minute: "2-digit",
      });
   };

   return (
      <Card className="border-border/50">
         <CardHeader>
            <CardTitle className="flex items-center gap-2">
               <ChatBubbleLeftRightIcon className="h-5 w-5" />
               Comments
            </CardTitle>
            <CardDescription>Manage comments for {albumName}</CardDescription>
         </CardHeader>
         <CardContent className="space-y-4">
            {/* Search */}
            <div className="relative">
               <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
               <Input
                  placeholder="Search comments by content, author name, or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
               />
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
               <TabsList className="grid w-full grid-cols-2 bg-astral-grey h-12">
                  <TabsTrigger
                     value="all"
                     className="flex items-center gap-2 h-full cursor-pointer"
                  >
                     All Comments
                     {allCount > 0 && (
                        <span className="bg-astral-grey-light text-muted-foreground px-2 py-0.5 rounded-full text-xs">
                           {allCount}
                        </span>
                     )}
                  </TabsTrigger>
                  <TabsTrigger
                     value="deleted"
                     className="flex items-center gap-2 h-full cursor-pointer"
                  >
                     Deleted Comments
                     {deletedCount > 0 && (
                        <span className="bg-destructive/20 text-destructive px-2 py-0.5 rounded-full text-xs">
                           {deletedCount}
                        </span>
                     )}
                  </TabsTrigger>
               </TabsList>

               <TabsContent value="all" className="space-y-4">
                  {commentsLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 1 }).map((_, i) => (
                           <div
                              key={i}
                              className="p-4 border border-border/50 rounded-lg space-y-3"
                           >
                              <div className="flex items-center gap-3">
                                 <Skeleton className="h-8 w-8 rounded-full" />
                                 <div className="space-y-1">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-32" />
                                 </div>
                              </div>
                              <Skeleton className="h-12 w-full" />
                           </div>
                        ))}
                     </div>
                  ) : comments && comments.length > 0 ? (
                     <>
                        <div className="space-y-4">
                           {comments.map((comment) => (
                              <CommentItem
                                 key={comment._id?.toString()}
                                 comment={comment}
                                 onSoftDelete={handleSoftDelete}
                                 onStartEditResponse={handleStartEditResponse}
                                 onRemoveResponse={handleRemoveResponse}
                                 editingResponse={editingResponse}
                                 responseText={responseText}
                                 setResponseText={setResponseText}
                                 onSaveResponse={handleSaveResponse}
                                 onCancelEdit={handleCancelEdit}
                                 formatDate={formatDate}
                                 isDeleted={false}
                              />
                           ))}
                        </div>

                        {!showMore && allCount > 2 && (
                           <div className="text-center">
                              <Button
                                 variant="outline"
                                 onClick={() => setShowMore(true)}
                              >
                                 Show More ({allCount - 2} remaining)
                              </Button>
                           </div>
                        )}
                     </>
                  ) : (
                     <div className="text-center py-8 text-muted-foreground">
                        <ChatBubbleOvalLeftEllipsisIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>
                           No comments found{" "}
                           {searchQuery && <span>for {searchQuery}</span>}
                        </p>
                     </div>
                  )}
               </TabsContent>

               <TabsContent value="deleted" className="space-y-4">
                  {commentsLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 1 }).map((_, i) => (
                           <div
                              key={i}
                              className="p-4 border border-border/50 rounded-lg space-y-3"
                           >
                              <div className="flex items-center gap-3">
                                 <Skeleton className="h-8 w-8 rounded-full" />
                                 <div className="space-y-1">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-32" />
                                 </div>
                              </div>
                              <Skeleton className="h-12 w-full" />
                           </div>
                        ))}
                     </div>
                  ) : comments && comments.length > 0 ? (
                     <div className="space-y-4">
                        {comments.map((comment) => (
                           <CommentItem
                              key={comment._id?.toString()}
                              comment={comment}
                              onRestore={handleRestore}
                              onPermanentDelete={handlePermanentDelete}
                              formatDate={formatDate}
                              isDeleted={true}
                           />
                        ))}
                     </div>
                  ) : (
                     <div className="text-center py-8 text-muted-foreground">
                        <TrashIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No deleted comments</p>
                     </div>
                  )}
               </TabsContent>
            </Tabs>
         </CardContent>

         {/* Delete Confirmation Dialog */}
         <AlertDialog
            open={deleteDialog.open}
            onOpenChange={(open) =>
               setDeleteDialog((prev) => ({ ...prev, open }))
            }
         >
            <AlertDialogContent>
               <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center gap-2">
                     <AlertTriangle className="h-5 w-5 text-destructive" />
                     {deleteDialog.permanent
                        ? "Permanently Delete Comment"
                        : "Delete Comment"}
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                     {deleteDialog.permanent
                        ? "This will permanently delete the comment and cannot be undone. The comment will be completely removed from the database."
                        : "This will hide the comment from public view but keep it in the database. You can restore it later if needed."}
                  </AlertDialogDescription>
               </AlertDialogHeader>
               <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                     onClick={confirmDelete}
                     className={cn(
                        deleteDialog.permanent &&
                           "bg-destructive hover:bg-destructive/90"
                     )}
                  >
                     {deleteDialog.permanent ? "Permanently Delete" : "Delete"}
                  </AlertDialogAction>
               </AlertDialogFooter>
            </AlertDialogContent>
         </AlertDialog>
      </Card>
   );
}

interface CommentItemProps {
   comment: CommentWithReplies;
   onSoftDelete?: (commentId: string) => void;
   onRestore?: (commentId: string) => void;
   onPermanentDelete?: (commentId: string) => void;
   onStartEditResponse?: (commentId: string, currentResponse?: string) => void;
   onRemoveResponse?: (commentId: string) => void;
   editingResponse?: string | null;
   responseText?: string;
   setResponseText?: (text: string) => void;
   onSaveResponse?: (commentId: string) => void;
   onCancelEdit?: () => void;
   formatDate: (date: Date | string) => string;
   isDeleted: boolean;
}

function CommentItem({
   comment,
   onSoftDelete,
   onRestore,
   onPermanentDelete,
   onStartEditResponse,
   onRemoveResponse,
   editingResponse,
   responseText,
   setResponseText,
   onSaveResponse,
   onCancelEdit,
   formatDate,
   isDeleted,
}: CommentItemProps) {
   const isEditing = editingResponse === comment._id;

   return (
      <div
         className={cn(
            "p-4 border border-border/50 rounded-lg space-y-2",
            isDeleted && "border-destructive/30 bg-destructive/5"
         )}
      >
         {/* Comment Header */}
         <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
               <Avatar name={comment.authorName} size="md" />
               <div>
                  <div className="flex items-center gap-2 text-sm">
                     <span className="font-medium">{comment.authorName}</span>
                     {isDeleted && (
                        <span className="text-xs bg-destructive/20 text-destructive px-2 py-0.5 rounded-full">
                           Deleted
                        </span>
                     )}
                  </div>
                  <div className="text-xs font-semibold text-muted-foreground">
                     {comment.authorEmail && `${comment.authorEmail} • `}
                     {formatDate(comment.createdAt)}
                     {comment.deletedAt &&
                        ` • Deleted ${formatDate(comment.deletedAt)}`}
                  </div>
               </div>
            </div>

            {/* Actions */}
            <DropdownMenu>
               <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="rounded-lg">
                     <MoreVertical className="size-4.5" />
                  </Button>
               </DropdownMenuTrigger>
               <DropdownMenuContent align="end">
                  {isDeleted ? (
                     <>
                        <DropdownMenuItem
                           onClick={() => onRestore?.(comment._id!.toString())}
                        >
                           <ArrowUturnLeftIcon className="h-4 w-4 mr-2" />
                           Restore
                        </DropdownMenuItem>
                        <DropdownMenuItem
                           onClick={() =>
                              onPermanentDelete?.(comment._id!.toString())
                           }
                           className="text-destructive"
                        >
                           <TrashIcon className="h-4 w-4 mr-2" />
                           Delete Permanently
                        </DropdownMenuItem>
                     </>
                  ) : (
                     <>
                        <DropdownMenuItem
                           onClick={() =>
                              onSoftDelete?.(comment._id!.toString())
                           }
                        >
                           <TrashIcon className="h-4 w-4 mr-2" />
                           Delete
                        </DropdownMenuItem>
                        {comment.adminResponse ? (
                           <>
                              <DropdownMenuItem
                                 onClick={() =>
                                    onStartEditResponse?.(
                                       comment._id!.toString(),
                                       comment.adminResponse
                                    )
                                 }
                              >
                                 <PencilSquareIcon className="h-4 w-4 mr-2" />
                                 Edit Response
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                 onClick={() =>
                                    onRemoveResponse?.(comment._id!.toString())
                                 }
                                 className="text-destructive"
                              >
                                 <X className="h-4 w-4 mr-2" />
                                 Remove Response
                              </DropdownMenuItem>
                           </>
                        ) : (
                           <DropdownMenuItem
                              onClick={() =>
                                 onStartEditResponse?.(comment._id!.toString())
                              }
                           >
                              <Reply className="h-4 w-4 mr-2" />
                              Add Response
                           </DropdownMenuItem>
                        )}
                     </>
                  )}
               </DropdownMenuContent>
            </DropdownMenu>
         </div>

         {/* Comment Content */}
         <div className="pl-11">
            <p className="text-sm leading-relaxed">{comment.content}</p>
         </div>

         {/* Admin Response Section */}
         {!isDeleted && comment.adminResponse && !isEditing && (
            <div className="pl-11 space-y-3">
               <div className="bg-primary/10 text-primary border border-primary/20 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                     <Avatar name="Admin" size="sm" />
                     <span className="text-sm font-medium text-red-700">
                        Astral Studio
                     </span>
                  </div>
                  <p className="text-sm text-red-600">
                     {comment.adminResponse}
                  </p>
               </div>

               {isEditing && (
                  <div className="space-y-3">
                     <Textarea
                        placeholder="Write your admin response..."
                        value={responseText}
                        onChange={(e) => setResponseText?.(e.target.value)}
                        rows={3}
                        className="resize-none"
                     />
                     <div className="flex items-center gap-2">
                        <Button
                           onClick={() =>
                              onSaveResponse?.(comment._id!.toString())
                           }
                           disabled={!responseText?.trim()}
                        >
                           <PaperAirplaneIcon className="h-4 w-4" />
                           Send Response
                        </Button>
                        <Button variant="outline" onClick={onCancelEdit}>
                           Cancel
                        </Button>
                     </div>
                  </div>
               )}
            </div>
         )}

         {/* Replies */}
         {comment.replies.length > 0 && (
            <div className="pl-11 space-y-3 border-l-2 border-muted ml-4">
               {comment.replies.map((reply) => (
                  <div key={reply._id?.toString()} className="pl-4">
                     <div className="flex items-center gap-2 mb-1">
                        <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                           <span className="text-xs font-medium">
                              {reply.authorName.charAt(0).toUpperCase()}
                           </span>
                        </div>
                        <span className="text-sm font-medium">
                           {reply.authorName}
                        </span>
                        <span className="text-xs text-muted-foreground">
                           {formatDate(reply.createdAt)}
                        </span>
                     </div>
                     <p className="text-sm text-muted-foreground pl-8">
                        {reply.content}
                     </p>
                  </div>
               ))}
            </div>
         )}
      </div>
   );
}
