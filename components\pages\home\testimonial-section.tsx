import TextReveal from "@/components/animations/text-reveal";
import { Skeleton } from "@/components/ui/skeleton";
import { testimonials } from "@/data";
import { useCombinedTestimonials } from "@/lib/hooks/use-testimonials";
import { Quote, <PERSON> } from "lucide-react";
import { motion } from "motion/react";

export default function TestimonialSection() {
   const { data: combinedTestimonials, isLoading } = useCombinedTestimonials();

   // Use combined testimonials if available, otherwise fallback to static testimonials
   const displayTestimonials = combinedTestimonials || testimonials;

   return (
      <>
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Love from{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Our Clients
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Don&apos;t just take our word for it. Here&apos;s what
                        our happy clients have to say about their experience
                        with Astral Studios.
                     </p>
                  </TextReveal>
               </div>

               {isLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                     {[1, 2, 3].map((index) => (
                        <div
                           key={index}
                           className="bg-card border border-astral-grey-light rounded-2xl p-8 shadow-card animate-pulse"
                        >
                           <div className="flex items-center space-x-1 mb-4">
                              {[...Array(5)].map((_, i) => (
                                 <Skeleton
                                    key={i}
                                    className="w-5 h-5 rounded"
                                 />
                              ))}
                           </div>
                           <div className="space-y-3 mb-4">
                              <Skeleton className="h-4 rounded w-full" />
                              <Skeleton className="h-4 rounded w-3/4" />
                           </div>
                           <div className="flex items-center space-x-3">
                              <Skeleton className="w-12 h-12 rounded-full" />
                              <div className="space-y-2">
                                 <Skeleton className="h-4 rounded w-24" />
                                 <Skeleton className="h-3 rounded w-20" />
                              </div>
                           </div>
                        </div>
                     ))}
                  </div>
               ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                     {displayTestimonials.map((testimonial, index) => (
                        <motion.div
                           key={index}
                           initial={{ opacity: 0, y: 20 }}
                           whileInView={{ opacity: 1, y: 0 }}
                           transition={{
                              duration: 0.6,
                              delay: index * 0.1 + 0.4,
                           }}
                           viewport={{
                              once: true,
                           }}
                           className="flex flex-col bg-card border border-astral-grey-light rounded-2xl p-8 shadow-card"
                        >
                           <div className="flex items-center space-x-1 mb-4">
                              {[...Array(testimonial.rating)].map((_, i) => (
                                 <span
                                    key={i}
                                    className="text-yellow-400 text-xl"
                                 >
                                    <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />{" "}
                                    {/* ★ */}
                                 </span>
                              ))}
                           </div>
                           <div className="relative">
                              <Quote className="h-10 w-10 text-astral-grey-light mb-2 absolute -top-5 -right-5" />
                              <p className="text-muted-foreground font-montserrat mb-4 italic z-10 relative">
                                 &quot;{testimonial.text}&quot;
                              </p>
                           </div>
                           <div className="flex items-center space-x-3 mt-auto">
                              <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center">
                                 <span className="text-white font-playfair font-semibold">
                                    {testimonial.initials}{" "}
                                 </span>
                              </div>
                              <div>
                                 <p className="font-playfair font-semibold text-foreground">
                                    {testimonial.name}
                                 </p>
                                 <p className="text-sm text-muted-foreground font-montserrat">
                                    {testimonial.service}
                                 </p>
                              </div>
                           </div>
                        </motion.div>
                     ))}
                  </div>
               )}
            </div>
         </section>
      </>
   );
}
