"use client";

import { useQuery } from "@tanstack/react-query";
import { getCombinedTestimonials, getTestimonialsForAlbum } from "@/lib/services/testimonial-service";

/**
 * Query keys for testimonials
 */
export const testimonialQueryKeys = {
   all: ["testimonials"] as const,
   combined: () => [...testimonialQueryKeys.all, "combined"] as const,
   byAlbum: (albumId: string) => [...testimonialQueryKeys.all, "album", albumId] as const,
};

/**
 * Hook to fetch combined testimonials (comments + fallback)
 */
export function useCombinedTestimonials() {
   return useQuery({
      queryKey: testimonialQueryKeys.combined(),
      queryFn: getCombinedTestimonials,
      staleTime: 10 * 60 * 1000, // 10 minutes
      gcTime: 30 * 60 * 1000, // 30 minutes
   });
}

/**
 * Hook to fetch testimonials for a specific album
 */
export function useAlbumTestimonials(albumId: string) {
   return useQuery({
      queryKey: testimonialQueryKeys.byAlbum(albumId),
      queryFn: () => getTestimonialsForAlbum(albumId),
      enabled: !!albumId,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 15 * 60 * 1000, // 15 minutes
   });
}
