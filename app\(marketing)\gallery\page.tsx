import GalleryPageContent from "@/components/pages/gallery/gallery-page-content";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
   title: "Gallery - Astral Studios",
   description:
      "Explore our stunning collection of photography and videography work, organized into beautiful albums.",
   keywords: [
      "gallery",
      "photography",
      "videography",
      "albums",
      "astral studios",
   ],
   openGraph: {
      title: "Gallery - Astral Studios",
      description:
         "Explore our stunning collection of photography and videography work, organized into beautiful albums.",
      type: "website",
      images: [
         {
            url: "/images/gallery-hero.jpg",
            width: 1200,
            height: 630,
            alt: "Astral Studios Gallery",
         },
      ],
   },
   twitter: {
      card: "summary_large_image",
      title: "Gallery - Astral Studios",
      description:
         "Explore our stunning collection of photography and videography work, organized into beautiful albums.",
      images: ["/images/gallery-hero.jpg"],
   },
};

export default function GalleryPage() {
   return (
      <Suspense fallback={<div>Loading gallery...</div>}>
         <GalleryPageContent />
      </Suspense>
   );
}
