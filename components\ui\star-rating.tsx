"use client";

import { cn } from "@/lib/utils";
import { Star } from "lucide-react";
import { useState } from "react";

interface StarRatingProps {
   value?: number;
   onChange?: (rating: number) => void;
   readonly?: boolean;
   size?: "sm" | "md" | "lg";
   className?: string;
   showLabel?: boolean;
   required?: boolean;
}

const sizeClasses = {
   sm: "h-4 w-4",
   md: "h-5 w-5",
   lg: "h-6 w-6",
};

const labels = {
   1: "Poor",
   2: "Fair",
   3: "Good",
   4: "Very Good",
   5: "Excellent",
};

export function StarRating({
   value = 0,
   onChange,
   readonly = false,
   size = "md",
   className,
   showLabel = false,
   required = false,
}: StarRatingProps) {
   const [hoverRating, setHoverRating] = useState(0);
   const [hasInteracted, setHasInteracted] = useState(false);

   const handleStarClick = (rating: number) => {
      if (readonly || !onChange) return;
      setHasInteracted(true);
      onChange(rating);
   };

   const handleStarHover = (rating: number) => {
      if (readonly) return;
      setHoverRating(rating);
   };

   const handleMouseLeave = () => {
      if (readonly) return;
      setHoverRating(0);
   };

   const displayRating = hoverRating || value;
   const showError = required && hasInteracted && value === 0;

   return (
      <div className={cn("flex flex-col gap-2 py-2", className)}>
         <div className="flex items-center gap-1">
            {[1, 2, 3, 4, 5].map((star) => {
               const isFilled = star <= displayRating;
               const isHovered = star <= hoverRating;

               return (
                  <button
                     key={star}
                     type="button"
                     onClick={() => handleStarClick(star)}
                     onMouseEnter={() => handleStarHover(star)}
                     onMouseLeave={handleMouseLeave}
                     disabled={readonly}
                     className={cn(
                        "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50 rounded",
                        readonly
                           ? "cursor-default"
                           : "cursor-pointer hover:scale-110",
                        showError && "animate-pulse"
                     )}
                     aria-label={`Rate ${star} star${star !== 1 ? "s" : ""}`}
                  >
                     <Star
                        className={cn(
                           sizeClasses[size],
                           "transition-colors duration-200",
                           isFilled
                              ? isHovered && !readonly
                                 ? "fill-yellow-400 text-yellow-400"
                                 : "fill-yellow-400 text-yellow-400"
                              : isHovered && !readonly
                              ? "fill-yellow-200 text-yellow-200"
                              : "fill-transparent text-gray-300 hover:text-yellow-300",
                           showError && "text-red-400"
                        )}
                     />
                  </button>
               );
            })}

            {showLabel && displayRating > 0 && (
               <span className="ml-2 text-sm text-muted-foreground">
                  {labels[displayRating as keyof typeof labels]}
               </span>
            )}
         </div>

         {showError && (
            <p className="text-sm text-red-500">Please select a rating</p>
         )}
      </div>
   );
}

export default StarRating;
