"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>gle, ChevronLeft, ChevronRight, X } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import Image from "next/image";
import { useCallback, useEffect, useState } from "react";

interface ImageGalleryProps {
   images: {
      src: string;
      alt: string;
      category?: string;
      width?: number;
      height?: number;
      displayOrder?: number;
      createdAt?: Date | string;
   }[];
   showCategory?: boolean;
   className?: string;
   sortByDisplayOrder?: boolean;
}

export function ImageGallery({
   images,
   showCategory = false,
   className = "",
   sortByDisplayOrder = false,
}: ImageGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<number | null>(null);
   const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
   const [imageLoading, setImageLoading] = useState<Set<string>>(new Set());

   // Reset error and loading states when images change
   useEffect(() => {
      setImageErrors(new Set());
      setImageLoading(new Set());
      setSelectedImage(null);
   }, [images]);

   const handleImageError = (imageUrl: string) => {
      setImageErrors((prev) => new Set(prev).add(imageUrl));
      setImageLoading((prev) => {
         const newSet = new Set(prev);
         newSet.delete(imageUrl);
         return newSet;
      });
   };

   const handleImageLoad = (imageUrl: string) => {
      setImageLoading((prev) => {
         const newSet = new Set(prev);
         newSet.delete(imageUrl);
         return newSet;
      });
   };

   const handleImageLoadStart = (imageUrl: string) => {
      setImageLoading((prev) => new Set(prev).add(imageUrl));
   };

   // Sort images based on display order if requested
   const sortedImages = sortByDisplayOrder
      ? [...images].sort((a, b) => {
           // Primary sort: by display order (ascending)
           const orderA = a.displayOrder ?? Number.MAX_SAFE_INTEGER;
           const orderB = b.displayOrder ?? Number.MAX_SAFE_INTEGER;

           if (orderA !== orderB) {
              return orderA - orderB;
           }

           // Secondary sort: by creation date (newest first) for same display order
           const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
           const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;

           return dateB - dateA;
        })
      : images;

   const openLightbox = (index: number) => {
      setSelectedImage(index);
   };

   const closeLightbox = useCallback(() => {
      setSelectedImage(null);
   }, []);

   const nextImage = useCallback(() => {
      if (selectedImage !== null) {
         setSelectedImage((selectedImage + 1) % sortedImages.length);
      }
   }, [selectedImage, sortedImages.length]);

   const prevImage = useCallback(() => {
      if (selectedImage !== null) {
         setSelectedImage(
            selectedImage === 0 ? sortedImages.length - 1 : selectedImage - 1
         );
      }
   }, [selectedImage, sortedImages.length]);

   // Keyboard navigation
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         if (selectedImage === null) return;

         switch (e.key) {
            case "Escape":
               closeLightbox();
               break;
            case "ArrowLeft":
               e.preventDefault();
               prevImage();
               break;
            case "ArrowRight":
               e.preventDefault();
               nextImage();
               break;
         }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
   }, [selectedImage, closeLightbox, nextImage, prevImage]);

   // Prevent body scrolling when lightbox is open
   useEffect(() => {
      if (selectedImage !== null) {
         document.body.style.overflow = "hidden";
      } else {
         document.body.style.overflow = "";
      }

      return () => {
         document.body.style.overflow = "";
      };
   }, [selectedImage]);

   return (
      <>
         {/* Gallery Grid */}
         <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 ${className}`}
         >
            {sortedImages.map((image, index) => (
               <motion.div
                  key={`${image.src}-${index}`}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="group relative overflow-hidden rounded-2xl bg-card border border-astral-grey-light hover:shadow-elegant cursor-pointer"
                  onClick={() =>
                     !imageErrors.has(image.src) && openLightbox(index)
                  }
               >
                  <div className="relative h-85 overflow-hidden">
                     {imageErrors.has(image.src) ? (
                        // Error state for failed images
                        <div className="w-full h-full flex flex-col items-center justify-center bg-muted text-muted-foreground">
                           <AlertTriangle className="w-8 h-8 mb-2" />
                           <span className="text-sm text-center px-4">
                              Failed to load image
                           </span>
                           <span className="text-xs text-center px-4 mt-1 opacity-70">
                              {image.alt}
                           </span>
                        </div>
                     ) : imageLoading.has(image.src) ? (
                        // Loading state
                        <div className="w-full h-full flex items-center justify-center bg-muted">
                           <div className="flex flex-col items-center gap-2 text-muted-foreground">
                              <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin" />
                              <span className="text-sm">Loading...</span>
                           </div>
                        </div>
                     ) : (
                        <Image
                           src={image.src}
                           alt={image.alt}
                           fill
                           className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                           onError={() => handleImageError(image.src)}
                           onLoad={() => handleImageLoad(image.src)}
                           onLoadStart={() => handleImageLoadStart(image.src)}
                           sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                     )}
                  </div>
                  {showCategory && image.category && (
                     <div className="p-6">
                        <div className="flex items-center justify-between">
                           <span className="text-sm font-montserrat font-medium text-primary bg-primary/10 px-3 py-1 rounded-full">
                              {image.category}
                           </span>
                        </div>
                     </div>
                  )}
               </motion.div>
            ))}
         </div>

         {/* Lightbox */}
         <AnimatePresence>
            {selectedImage !== null && (
               <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="fixed inset-0 z-50 flex items-center justify-center p-4"
               >
                  {/* Backdrop */}
                  <motion.div
                     initial={{ opacity: 0 }}
                     animate={{ opacity: 1 }}
                     exit={{ opacity: 0 }}
                     transition={{ duration: 0.3 }}
                     className="fixed inset-0 bg-black/90 backdrop-blur-sm"
                     onClick={closeLightbox}
                  />

                  {/* Close Button */}
                  <Button
                     variant="ghost"
                     size="icon"
                     className="absolute top-4 right-4 bg-white/20 rounded-full sm:bg-transparent text-white size-12 hover:bg-white/30 z-10 transition-all duration-200"
                     onClick={closeLightbox}
                  >
                     <X className="size-6 sm:size-8" />
                  </Button>

                  {/* Navigation Buttons */}
                  {sortedImages.length > 1 && (
                     <>
                        <Button
                           variant="ghost"
                           size="icon"
                           className="absolute bg-gradient-accent size-12 rounded-full left-8 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10 transition-all duration-200 hover:scale-110"
                           onClick={prevImage}
                        >
                           <ChevronLeft className="size-6" />
                        </Button>

                        <Button
                           variant="ghost"
                           size="icon"
                           className="absolute bg-gradient-accent size-12 rounded-full right-8 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 z-10 transition-all duration-200 hover:scale-110"
                           onClick={nextImage}
                        >
                           <ChevronRight className="size-6" />
                        </Button>
                     </>
                  )}

                  {/* Image Container */}
                  <motion.div
                     initial={{ scale: 0.8, opacity: 0 }}
                     animate={{ scale: 1, opacity: 1 }}
                     exit={{ scale: 0.8, opacity: 0 }}
                     transition={{
                        duration: 0.4,
                        ease: [0.4, 0, 0.2, 1],
                        opacity: { duration: 0.3 },
                     }}
                     className="relative flex items-center justify-center w-full h-full max-w-[95vw] max-h-[95vh]"
                  >
                     {imageErrors.has(sortedImages[selectedImage].src) ? (
                        // Error state in lightbox
                        <div className="w-full h-full flex flex-col items-center justify-center bg-black/50 rounded-2xl text-white max-w-md max-h-md">
                           <AlertTriangle className="w-16 h-16 mb-4" />
                           <h3 className="text-xl font-semibold mb-2">
                              Image Not Available
                           </h3>
                           <p className="text-center text-white/80 max-w-md px-4">
                              This image could not be loaded. It may have been
                              moved or deleted.
                           </p>
                           <p className="text-sm text-white/60 mt-2 px-4 text-center">
                              {sortedImages[selectedImage].alt}
                           </p>
                        </div>
                     ) : (
                        <div className="relative w-full h-full flex items-center justify-center">
                           <Image
                              src={sortedImages[selectedImage].src}
                              alt={sortedImages[selectedImage].alt}
                              fill
                              className="object-contain rounded-2xl"
                              style={{
                                 maxWidth: "95vw",
                                 maxHeight: "95vh",
                              }}
                              onError={() =>
                                 handleImageError(
                                    sortedImages[selectedImage].src
                                 )
                              }
                              priority
                              sizes="95vw"
                           />
                        </div>
                     )}
                  </motion.div>
               </motion.div>
            )}
         </AnimatePresence>
      </>
   );
}
