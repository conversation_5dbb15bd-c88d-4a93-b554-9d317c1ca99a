"use server";

import { extractFileKeyFromUrl } from "@/lib/cloudflare/helpers";
import {
   CreateImageInput,
   DEFAULT_PAGINATION,
   Image,
   ImageDocument,
   PaginatedResponse,
   PaginationOptions,
   UpdateImageInput,
   createImageMetadata,
   createPaginationMetadata,
   validateImageInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get all images with pagination and filtering
 */
export async function getImages(
   options: PaginationOptions & {
      albumId?: string | null;
   } = {}
): Promise<PaginatedResponse<Image>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
         albumId,
      } = options;

      const collection = await getCollection<ImageDocument>("images");

      // Build filter query
      const filter: Record<string, unknown> = {};

      if (albumId !== undefined) {
         filter.albumId = albumId;
      }

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error fetching images:", error);
      throw new Error("Failed to fetch images");
   }
}

/**
 * Get ungrouped images (albumId is null)
 */
export async function getUngroupedImages(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   return getImages({ ...options, albumId: null });
}

/**
 * Get image by ID
 */
export async function getImageById(id: string): Promise<Image | null> {
   try {
      const collection = await getCollection<ImageDocument>("images");
      const image = await collection.findOne({ _id: new ObjectId(id) });

      if (!image) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...image,
         _id: image._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching image by ID:", error);
      throw new Error("Failed to fetch image");
   }
}

/**
 * Create a new image
 */
export async function createImage(input: CreateImageInput): Promise<Image> {
   try {
      // Validate input
      const errors = validateImageInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<ImageDocument>("images");
      const imageData = createImageMetadata(input);

      const result = await collection.insertOne(imageData);

      if (!result.insertedId) {
         throw new Error("Failed to create image");
      }

      const createdImage = await collection.findOne({ _id: result.insertedId });

      if (!createdImage) {
         throw new Error("Failed to retrieve created image");
      }

      // Convert ObjectId to string for client serialization
      return {
         ...createdImage,
         _id: createdImage._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating image:", error);
      throw error;
   }
}

/**
 * Update an image
 */
export async function updateImage(
   id: string,
   input: UpdateImageInput
): Promise<Image | null> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating image:", error);
      throw new Error("Failed to update image");
   }
}

/**
 * Delete an image
 */
export async function deleteImage(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<ImageDocument>("images");
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting image:", error);
      throw new Error("Failed to delete image");
   }
}

/**
 * Get images by album ID
 */
export async function getImagesByAlbumId(
   albumId: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   return getImages({ ...options, albumId });
}

/**
 * Search images by name
 */
export async function searchImages(
   query: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const collection = await getCollection<ImageDocument>("images");

      // Build search filter
      const filter = {
         name: { $regex: query, $options: "i" },
      };

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error searching images:", error);
      throw new Error("Failed to search images");
   }
}

/**
 * Move image to a different album
 */
export async function moveImageToAlbum(
   imageId: string,
   albumId: string
): Promise<Image | null> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(imageId) },
         {
            $set: {
               albumId: albumId,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error moving image to album:", error);
      throw new Error("Failed to move image to album");
   }
}

/**
 * Delete image and its file from R2 storage
 */
export async function deleteImageAndFile(imageId: string): Promise<boolean> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      // First get the image to extract the file key for deletion
      const image = await collection.findOne({ _id: new ObjectId(imageId) });

      if (!image) {
         return false;
      }

      // Extract file key from URL using helper function
      const fileKey = extractFileKeyFromUrl(image.url);

      // Delete from database
      const deleteResult = await collection.deleteOne({
         _id: new ObjectId(imageId),
      });

      if (deleteResult.deletedCount > 0) {
         // Delete from R2 storage
         try {
            const { deleteFile } = await import("@/lib/cloudflare/cloudflare");
            await deleteFile(fileKey);
         } catch (storageError) {
            console.error("Error deleting file from R2 storage:", storageError);
            // Re-throw the error to ensure the caller knows the R2 deletion failed
            throw new Error(
               `Failed to delete file from R2 storage: ${
                  storageError instanceof Error
                     ? storageError.message
                     : "Unknown error"
               }`
            );
         }
         return true;
      }

      return false;
   } catch (error) {
      console.error("Error deleting image:", error);
      throw new Error("Failed to delete image");
   }
}

/**
 * Bulk move images to album
 */
export async function bulkMoveImagesToAlbum(
   imageIds: string[],
   albumId: string
): Promise<boolean> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      const objectIds = imageIds.map((id) => new ObjectId(id));

      const result = await collection.updateMany(
         { _id: { $in: objectIds } },
         {
            $set: {
               albumId: albumId,
               updatedAt: new Date(),
            },
         }
      );

      return result.modifiedCount > 0;
   } catch (error) {
      console.error("Error bulk moving images to album:", error);
      throw new Error("Failed to move images to album");
   }
}

/**
 * Bulk delete images and their files from R2 storage
 */
export async function bulkDeleteImagesAndFiles(
   imageIds: string[]
): Promise<boolean> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      // First get all images to extract file keys for deletion
      const objectIds = imageIds.map((id) => new ObjectId(id));
      const images = await collection
         .find({ _id: { $in: objectIds } })
         .toArray();

      if (images.length === 0) {
         return false;
      }

      // Extract file keys from URLs using helper function
      const fileKeys = images.map((image) => extractFileKeyFromUrl(image.url));

      // Delete from database
      const deleteResult = await collection.deleteMany({
         _id: { $in: objectIds },
      });

      if (deleteResult.deletedCount > 0) {
         // Delete from R2 storage
         try {
            const { deleteFile } = await import("@/lib/cloudflare/cloudflare");
            const deletePromises = fileKeys.map(async (key) => {
               try {
                  await deleteFile(key);
                  console.log(`Successfully deleted file from R2: ${key}`);
                  return { success: true, key };
               } catch (error) {
                  console.error(`Failed to delete file from R2: ${key}`, error);
                  return { success: false, key, error };
               }
            });

            const results = await Promise.allSettled(deletePromises);
            const failedDeletions = results.filter(
               (
                  result
               ): result is PromiseFulfilledResult<{
                  success: false;
                  key: string;
                  error: unknown;
               }> => result.status === "fulfilled" && !result.value.success
            );

            if (failedDeletions.length > 0) {
               console.error(
                  `Failed to delete ${failedDeletions.length} files from R2:`,
                  failedDeletions
               );
               throw new Error(
                  `Failed to delete ${failedDeletions.length} files from R2 storage`
               );
            }
         } catch (storageError) {
            console.error(
               "Error deleting files from R2 storage:",
               storageError
            );
            // Re-throw the error to ensure the caller knows the R2 deletion failed
            throw new Error(
               `Failed to delete files from R2 storage: ${
                  storageError instanceof Error
                     ? storageError.message
                     : "Unknown error"
               }`
            );
         }
         return true;
      }

      return false;
   } catch (error) {
      console.error("Error bulk deleting images:", error);
      throw new Error("Failed to delete images");
   }
}

/**
 * Get recent images with album information
 */
export async function getRecentImages(
   limit: number = 6
): Promise<Array<Image & { albumName?: string; collectionNames?: string[] }>> {
   try {
      const collection = await getCollection<ImageDocument>("images");
      const albumCollection = await getCollection("albums");

      // Get recent images sorted by creation date
      const images = await collection
         .find({})
         .sort({ createdAt: -1 })
         .limit(limit)
         .toArray();

      // Get unique album and collection IDs
      const albumIds = [
         ...new Set(
            images
               .map((img) => img.albumId)
               .filter((id): id is string => id !== null)
         ),
      ];

      // Fetch albums
      const [albums] = await Promise.all([
         albumIds.length > 0
            ? albumCollection
                 .find({ _id: { $in: albumIds.map((id) => new ObjectId(id)) } })
                 .toArray()
            : [],
      ]);

      // Create lookup maps
      const albumMap = new Map(
         albums.map((album) => [album._id?.toString(), album.name])
      );

      // Enhance images with album names
      const enhancedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
         albumName: image.albumId ? albumMap.get(image.albumId) : undefined,
      }));

      return enhancedImages;
   } catch (error) {
      console.error("Error fetching recent images:", error);
      throw new Error("Failed to fetch recent images");
   }
}
