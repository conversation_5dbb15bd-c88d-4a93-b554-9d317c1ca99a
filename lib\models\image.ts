import { ObjectId } from "mongodb";

// Size classification based on image dimensions
export type ImageSize = "small" | "medium" | "large";

// Aspect ratio classification
export type ImageAspectRatio = "wide" | "tall" | "normal";

// Base image interface for database storage
export interface Image {
   _id?: string; // Serialized as string for client components
   url: string;
   size: ImageSize;
   name: string;
   albumId: string | null;
   width: number;
   height: number;
   aspectRatio: ImageAspectRatio;
   fileSize: number; // in bytes
   mimeType: string;
   createdAt: Date;
   updatedAt: Date;
}

// Database image interface with ObjectId (for server-side operations)
export interface ImageDocument {
   _id?: ObjectId;
   url: string;
   size: ImageSize;
   name: string;
   albumId: string | null;
   width: number;
   height: number;
   aspectRatio: ImageAspectRatio;
   fileSize: number; // in bytes
   mimeType: string;
   createdAt: Date;
   updatedAt: Date;
}

// Image creation input (without auto-generated fields)
export interface CreateImageInput {
   url: string;
   name: string;
   albumId?: string | null;
   width: number;
   height: number;
   fileSize: number;
   mimeType: string;
}

// Image update input (partial fields that can be updated)
export interface UpdateImageInput {
   name?: string;
   albumId?: string | null;
}

// Image with metadata for frontend display
export interface ImageWithMetadata extends Image {
   downloadUrl?: string;
   thumbnailUrl?: string;
}

// Size classification thresholds
export const SIZE_THRESHOLDS = {
   small: { maxWidth: 800, maxHeight: 600 },
   medium: { maxWidth: 1920, maxHeight: 1080 },
   // large: anything above medium
} as const;

// Aspect ratio thresholds
export const ASPECT_RATIO_THRESHOLDS = {
   wide: 1.5, // width/height > 1.5
   tall: 0.67, // width/height < 0.67 (1/1.5)
   // normal: between 0.67 and 1.5
} as const;

/**
 * Classify image size based on dimensions
 */
export function classifyImageSize(width: number, height: number): ImageSize {
   if (
      width <= SIZE_THRESHOLDS.small.maxWidth &&
      height <= SIZE_THRESHOLDS.small.maxHeight
   ) {
      return "small";
   }
   if (
      width <= SIZE_THRESHOLDS.medium.maxWidth &&
      height <= SIZE_THRESHOLDS.medium.maxHeight
   ) {
      return "medium";
   }
   return "large";
}

/**
 * Classify image aspect ratio
 */
export function classifyImageAspectRatio(
   width: number,
   height: number
): ImageAspectRatio {
   const ratio = width / height;

   if (ratio > ASPECT_RATIO_THRESHOLDS.wide) {
      return "wide";
   }
   if (ratio < ASPECT_RATIO_THRESHOLDS.tall) {
      return "tall";
   }
   return "normal";
}

/**
 * Create image metadata from input
 */
export function createImageMetadata(
   input: CreateImageInput
): Omit<Image, "_id"> {
   const now = new Date();

   return {
      ...input,
      albumId: input.albumId || null,
      size: classifyImageSize(input.width, input.height),
      aspectRatio: classifyImageAspectRatio(input.width, input.height),
      createdAt: now,
      updatedAt: now,
   };
}

/**
 * Validate image input
 */
export function validateImageInput(input: CreateImageInput): string[] {
   const errors: string[] = [];

   if (!input.url || typeof input.url !== "string") {
      errors.push("URL is required and must be a string");
   }

   if (!input.name || typeof input.name !== "string") {
      errors.push("Name is required and must be a string");
   }

   if (typeof input.width !== "number" || input.width <= 0) {
      errors.push("Width must be a positive number");
   }

   if (typeof input.height !== "number" || input.height <= 0) {
      errors.push("Height must be a positive number");
   }

   if (typeof input.fileSize !== "number" || input.fileSize <= 0) {
      errors.push("File size must be a positive number");
   }

   if (!input.mimeType || typeof input.mimeType !== "string") {
      errors.push("MIME type is required and must be a string");
   }

   // Validate MIME type is an image
   if (input.mimeType && !input.mimeType.startsWith("image/")) {
      errors.push("MIME type must be an image type");
   }

   return errors;
}
