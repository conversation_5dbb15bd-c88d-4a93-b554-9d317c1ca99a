"use client";

import TextReveal from "@/components/animations/text-reveal";
import {
   GalleryGrid,
   GallerySearch,
   LoadMoreButton,
} from "@/components/pages/gallery";
import AlbumCard from "@/components/pages/gallery/album/album-card";
import { StructuredData } from "@/components/structured-data";
import { Button } from "@/components/ui/button";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
   usePublicAlbums,
   useSearchAlbums,
   useSearchGalleryItems,
} from "@/lib/hooks/use-albums";
import Lenis from "@studio-freight/lenis";
import { Grid3X3, SortAsc, SortDesc } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

const ITEMS_PER_PAGE = 4;

export default function GalleryPageContent() {
   const [searchQuery, setSearchQuery] = useState("");
   const [currentPage, setCurrentPage] = useState(1);
   const [sortBy, setSortBy] = useState("createdAt");
   const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
   const [filters, setFilters] = useState<Record<string, string>>({});

   // Reset page when search or filters change
   useEffect(() => {
      setCurrentPage(1);
   }, [searchQuery, filters, sortBy, sortOrder]);

   // Fetch data based on search or normal listing
   const isSearching = searchQuery.trim().length > 0;

   const { data: albumsData, isLoading: albumsLoading } = usePublicAlbums({
      page: currentPage,
      limit: ITEMS_PER_PAGE,
      sortBy,
      sortOrder,
   });

   const { data: searchData, isLoading: searchLoading } = useSearchAlbums(
      searchQuery,
      {
         page: currentPage,
         limit: ITEMS_PER_PAGE,
         sortBy,
         sortOrder,
      }
   );

   // Determine which data to use
   const currentData = isSearching ? searchData : albumsData;
   const isLoading = isSearching ? searchLoading : albumsLoading;
   const albums = currentData?.data || [];
   const pagination = currentData?.pagination;

   const handleLoadMore = () => {
      if (pagination?.hasNext) {
         setCurrentPage((prev) => prev + 1);
      }
   };

   const handleSortChange = (value: string) => {
      const [newSortBy, newSortOrder] = value.split("-");
      setSortBy(newSortBy);
      setSortOrder(newSortOrder as "asc" | "desc");
   };

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   // Search functionality
   const { data: searchResults } = useSearchGalleryItems(searchQuery, {
      limit: 12,
   });

   // Memoized filtered data
   const { displayAlbums } = useMemo(() => {
      if (searchQuery.trim()) {
         return {
            displayAlbums: searchResults?.albums.data || [],
            hasMoreAlbums: false,
         };
      }

      const albums = albumsData?.data || [];

      return {
         displayAlbums: albums,
         hasMoreAlbums:
            albums.length >= ITEMS_PER_PAGE && albumsData?.pagination.hasNext,
      };
   }, [searchQuery, searchResults, albumsData]);

   return (
      <div className="min-h-screen">
         {/* Structured Data */}
         <StructuredData
            type="imageGallery"
            data={{
               name: "Astral Studios Gallery",
               description:
                  "Explore our stunning collection of photography and videography work, organized into beautiful albums that showcase the artistry and emotion of every moment we capture.",
               url: "https://www.astralstudios.co.uk/gallery",
               albums: displayAlbums.slice(0, 10).map((album) => ({
                  name: album.name,
                  description: album.description,
                  url: `https://www.astralstudios.co.uk/gallery/albums/${album._id}`,
                  coverImageUrl: album.coverImageUrl,
                  createdAt: album.createdAt,
               })),
            }}
         />

         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4">
               <div className="text-center max-w-4xl mx-auto">
                  <TextReveal>
                     <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6 leading-18">
                        Our{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Gallery
                        </span>
                     </h1>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                        Explore our stunning collection of photography and
                        videography work, organized into beautiful albums that
                        showcase the artistry and emotion of every moment we
                        capture.
                     </p>
                  </TextReveal>
                  {/* <ElementReveal className="flex flex-col sm:flex-row items-center justify-center gap-4">
                     <Button asChild size="lg">
                        <Link href="/gallery/albums">View All Albums</Link>
                     </Button>
                  </ElementReveal> */}
               </div>
            </div>
         </section>

         <div className="container mx-auto px-4 mb-12">
            <div className="mt-8 mb-12">
               {/* Search */}
               <GallerySearch
                  onSearch={setSearchQuery}
                  placeholder="Search albums by name..."
                  className="mb-8"
               />

               {/* Controls */}
               <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  <div />

                  {/* Sort */}
                  <div className="flex items-center gap-2">
                     <span className="text-sm text-muted-foreground">
                        Sort by:
                     </span>
                     <Select
                        value={`${sortBy}-${sortOrder}`}
                        onValueChange={handleSortChange}
                     >
                        <SelectTrigger className="w-48">
                           <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="createdAt-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Newest First
                              </div>
                           </SelectItem>
                           <SelectItem value="createdAt-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Oldest First
                              </div>
                           </SelectItem>
                           <SelectItem value="name-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Name A-Z
                              </div>
                           </SelectItem>
                           <SelectItem value="name-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Name Z-A
                              </div>
                           </SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </div>
            </div>

            {/* Results Info */}
            {isSearching && (
               <div className="mb-8">
                  {searchLoading ? (
                     <p className="text-muted-foreground">
                        Searching albums...
                     </p>
                  ) : (
                     <p className="text-muted-foreground">
                        {albums.length > 0 ? (
                           <>
                              Found {pagination?.total || albums.length} albums
                              for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        ) : (
                           <>
                              No albums found for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        )}
                     </p>
                  )}
               </div>
            )}

            {/* Albums Grid */}
            {isLoading ? (
               <GalleryGrid columns={4}>
                  {Array.from({ length: 4 }).map((_, i) => (
                     <div key={i} className="space-y-4">
                        <Skeleton className="aspect-[4/3] rounded-2xl" />
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                     </div>
                  ))}
               </GalleryGrid>
            ) : albums.length > 0 ? (
               <>
                  <GalleryGrid columns={4} className="mb-12">
                     {albums.map((album) => (
                        <AlbumCard
                           key={String(album._id)}
                           album={album}
                           variant="default"
                           showStats={true}
                           showDescription={true}
                        />
                     ))}
                  </GalleryGrid>

                  {/* Pagination */}
                  {pagination && (
                     <div className="flex flex-col items-center gap-4">
                        {/* Load More Button */}
                        {pagination.hasNext && (
                           <LoadMoreButton
                              onClick={handleLoadMore}
                              isLoading={isLoading}
                              hasMore={pagination.hasNext}
                              buttonText="Load More Albums"
                              loadingText="Loading more albums..."
                           />
                        )}

                        {/* Pagination Info */}
                        <div className="text-sm text-muted-foreground text-center">
                           Showing {albums.length} of {pagination.total} albums
                           {pagination.totalPages > 1 && (
                              <span className="ml-2">
                                 (Page {pagination.page} of{" "}
                                 {pagination.totalPages})
                              </span>
                           )}
                        </div>
                     </div>
                  )}
               </>
            ) : (
               <div className="text-center py-20">
                  <Grid3X3 className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
                  <h3 className="text-2xl font-semibold text-foreground mb-4">
                     {isSearching ? "No Albums Found" : "No Albums Available"}
                  </h3>
                  <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                     {isSearching
                        ? "Try adjusting your search terms or filters to find what you're looking for."
                        : "We're working on adding new albums. Check back soon!"}
                  </p>
                  {isSearching && (
                     <Button
                        onClick={() => {
                           setSearchQuery("");
                           setFilters({});
                        }}
                        variant="outline"
                     >
                        Clear Search
                     </Button>
                  )}
               </div>
            )}
         </div>
      </div>
   );
}
