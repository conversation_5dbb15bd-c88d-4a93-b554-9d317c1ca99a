"use server";

import {
   deleteFile as deleteFileFromR2,
   generatePresignedUrl,
   uploadFileFromFile,
} from "@/lib/cloudflare/cloudflare";
import {
   extractFileKeyFromUrl,
   generateUniqueKey,
} from "@/lib/cloudflare/helpers";
import { ApiResponse, CreateImageInput } from "@/lib/models";
import { createImage } from "@/lib/services/image-service";
import { validateImageFile } from "@/lib/utils/image-processing";

/**
 * Upload image to Cloudflare R2 and save metadata to database
 */
export async function uploadImage(formData: FormData): Promise<ApiResponse> {
   try {
      const file = formData.get("file") as File;
      const albumId = formData.get("albumId") as string | null;
      const width = parseInt(formData.get("width") as string);
      const height = parseInt(formData.get("height") as string);

      if (!file) {
         return {
            success: false,
            error: "No file provided",
         };
      }

      // Validate file
      const validationErrors = validateImageFile(file);
      if (validationErrors.length > 0) {
         return {
            success: false,
            error: validationErrors.join(", "),
         };
      }

      // Upload to R2
      const { url: imageUrl } = await uploadFileFromFile(file);

      // Create image metadata
      const imageInput: CreateImageInput = {
         url: imageUrl,
         name: file.name,
         albumId: albumId || null,
         width,
         height,
         fileSize: file.size,
         mimeType: file.type,
      };

      // Save to database
      const savedImage = await createImage(imageInput);

      return {
         success: true,
         data: savedImage,
         message: "Image uploaded successfully",
      };
   } catch (error) {
      console.error("Error uploading image:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to upload image";

      if (error instanceof Error) {
         if (error.message.includes("File size too large")) {
            errorMessage = error.message;
         } else if (error.message.includes("File type not supported")) {
            errorMessage = error.message;
         } else if (error.message.includes("File is empty")) {
            errorMessage = error.message;
         } else if (error.message.includes("body size limit")) {
            errorMessage =
               "File size exceeds the maximum allowed limit of 50MB. Please try uploading a smaller file.";
         } else if (error.message.includes("timeout")) {
            errorMessage =
               "Upload timed out. The file might be too large or your connection is slow. Please try again.";
         } else {
            errorMessage = error.message;
         }
      }

      return {
         success: false,
         error: errorMessage,
      };
   }
}

/**
 * Upload multiple images
 */
export async function uploadMultipleImages(
   formData: FormData
): Promise<ApiResponse> {
   try {
      const files = formData.getAll("files") as File[];
      const albumId = formData.get("albumId") as string | null;

      if (!files || files.length === 0) {
         return {
            success: false,
            error: "No files provided",
         };
      }

      const results = [];
      const errors = [];

      for (const file of files) {
         try {
            // Get dimensions from form data (should be provided by client)
            const width = parseInt(
               formData.get(`width_${file.name}`) as string
            );
            const height = parseInt(
               formData.get(`height_${file.name}`) as string
            );

            const singleFormData = new FormData();
            singleFormData.append("file", file);
            singleFormData.append("albumId", albumId || "");
            singleFormData.append("width", width.toString());
            singleFormData.append("height", height.toString());

            const result = await uploadImage(singleFormData);

            if (result.success) {
               results.push(result.data);
            } else {
               errors.push(`${file.name}: ${result.error}`);
            }
         } catch (error) {
            let errorMessage = "Unknown error";

            if (error instanceof Error) {
               if (error.message.includes("File size too large")) {
                  errorMessage = error.message;
               } else if (error.message.includes("File type not supported")) {
                  errorMessage = error.message;
               } else if (error.message.includes("File is empty")) {
                  errorMessage = error.message;
               } else if (error.message.includes("body size limit")) {
                  errorMessage =
                     "File size exceeds the maximum allowed limit of 50MB";
               } else if (error.message.includes("timeout")) {
                  errorMessage = "Upload timed out - file might be too large";
               } else {
                  errorMessage = error.message;
               }
            }

            errors.push(`${file.name}: ${errorMessage}`);
         }
      }

      if (errors.length > 0 && results.length === 0) {
         return {
            success: false,
            error: `All uploads failed: ${errors.join(", ")}`,
         };
      }

      return {
         success: true,
         data: {
            uploaded: results,
            errors: errors.length > 0 ? errors : undefined,
         },
         message: `Successfully uploaded ${results.length} of ${files.length} images`,
      };
   } catch (error) {
      console.error("Error uploading multiple images:", error);

      let errorMessage = "Failed to upload images";

      if (error instanceof Error) {
         if (error.message.includes("body size limit")) {
            errorMessage =
               "Total file size exceeds the maximum allowed limit of 50MB. Please try uploading fewer or smaller files.";
         } else if (error.message.includes("timeout")) {
            errorMessage =
               "Upload timed out. Please try uploading fewer files at once.";
         } else {
            errorMessage = error.message;
         }
      }

      return {
         success: false,
         error: errorMessage,
      };
   }
}

/**
 * Generate pre-signed URL for direct client upload
 */
export async function generateUploadUrl(
   filename: string,
   contentType: string,
   fileSize: number
): Promise<ApiResponse> {
   try {
      // Validate file size (50MB limit)
      const maxSizeBytes = 50 * 1024 * 1024; // 50MB
      if (fileSize > maxSizeBytes) {
         return {
            success: false,
            error: `File size too large. Maximum size is 50MB.`,
         };
      }

      // Validate content type
      const supportedTypes = [
         "image/jpeg",
         "image/jpg",
         "image/png",
         "image/gif",
         "image/webp",
         "image/svg+xml",
      ];

      if (!supportedTypes.includes(contentType)) {
         return {
            success: false,
            error: "File type not supported. Please upload JPEG, PNG, GIF, WebP, or SVG images.",
         };
      }

      // Generate unique key
      const key = generateUniqueKey(filename);

      // Generate pre-signed URL
      const presignedUrl = await generatePresignedUrl(key, contentType);

      return {
         success: true,
         data: {
            uploadUrl: presignedUrl,
            key: key,
            publicUrl: `${process.env.R2_APP_DOMAIN}/${key}`,
         },
         message: "Upload URL generated successfully",
      };
   } catch (error) {
      console.error("Error generating upload URL:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to generate upload URL",
      };
   }
}

/**
 * Save image metadata after successful direct upload
 */
export async function saveImageMetadata(
   key: string,
   filename: string,
   contentType: string,
   fileSize: number,
   width: number,
   height: number,
   albumId?: string
): Promise<ApiResponse> {
   try {
      const imageInput: CreateImageInput = {
         url: `${process.env.R2_APP_DOMAIN}/${key}`,
         name: filename,
         albumId: albumId || null,
         width,
         height,
         fileSize,
         mimeType: contentType,
      };

      const savedImage = await createImage(imageInput);

      return {
         success: true,
         data: savedImage,
         message: "Image metadata saved successfully",
      };
   } catch (error) {
      console.error("Error saving image metadata:", error);
      return {
         success: false,
         error:
            error instanceof Error
               ? error.message
               : "Failed to save image metadata",
      };
   }
}

/**
 * Delete image from R2 and database
 */
export async function deleteImageAction(imageId: string): Promise<ApiResponse> {
   try {
      // First get the image to get the R2 key
      const { getImageById, deleteImage } = await import(
         "@/lib/services/image-service"
      );
      const image = await getImageById(imageId);

      if (!image) {
         return {
            success: false,
            error: "Image not found",
         };
      }

      // Extract key from URL using helper function
      const key = extractFileKeyFromUrl(image.url);

      // Delete from R2
      await deleteFileFromR2(key);

      // Delete from database
      const deleted = await deleteImage(imageId);

      if (!deleted) {
         return {
            success: false,
            error: "Failed to delete image from database",
         };
      }

      return {
         success: true,
         message: "Image deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting image:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to delete image",
      };
   }
}
