"use server";

import { Testimonial, testimonials } from "@/data/testimonials";
import { Comment } from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { getAlbumById } from "./album-service";

/**
 * Generate initials from a name
 */
function generateInitials(name: string): string {
   const words = name.trim().split(/\s+/);

   if (words.length === 1) {
      // Single name - use first letter
      return words[0].charAt(0).toUpperCase();
   } else if (words.length >= 2) {
      // Multiple names - use first letter of first and last word
      return (
         words[0].charAt(0) + words[words.length - 1].charAt(0)
      ).toUpperCase();
   }

   return "?";
}

/**
 * Transform a comment into testimonial format
 */
async function transformCommentToTestimonial(
   comment: Comment
): Promise<Testimonial | null> {
   try {
      // Skip comments without ratings (replies)
      if (!comment.rating || comment.rating === 0) {
         return null;
      }

      // Get album information to determine service name
      const album = await getAlbumById(comment.albumId);
      if (!album) {
         return null;
      }

      return {
         name: comment.authorName,
         initials: generateInitials(comment.authorName),
         service: album.name,
         rating: comment.rating,
         text: comment.content,
      };
   } catch (error) {
      console.error("Error transforming comment to testimonial:", error);
      return null;
   }
}

/**
 * Get testimonials from comments with ratings
 */
export async function getTestimonialsFromComments(): Promise<Testimonial[]> {
   try {
      const commentCollection = await getCollection<Comment>("comments");

      // Get approved comments with ratings (top-level comments only)
      const comments = await commentCollection
         .find({
            isApproved: true,
            // deletedAt: { $exists: false },
            // parentId: { $exists: false }, // Only top-level comments
            // rating: { $exists: true, $gte: 1 }, // Must have a rating
         })
         .sort({ createdAt: -1 }) // Most recent first
         .limit(20) // Limit to prevent too many database calls
         .toArray();

      // Transform comments to testimonials
      const testimonialPromises = comments.map((comment) =>
         transformCommentToTestimonial(comment)
      );

      const testimonialResults = await Promise.all(testimonialPromises);

      // Filter out null results and return valid testimonials
      return testimonialResults.filter(
         (testimonial): testimonial is Testimonial => testimonial !== null
      );
   } catch (error) {
      console.error("Error fetching testimonials from comments:", error);
      return [];
   }
}

/**
 * Get combined testimonials (comments + fallback data)
 * Ensures minimum of 3 testimonials are always returned
 */
export async function getCombinedTestimonials(): Promise<Testimonial[]> {
   try {
      // Get testimonials from comments
      const commentTestimonials = await getTestimonialsFromComments();

      // If we have 3 or more testimonials from comments, return them
      if (commentTestimonials.length >= 3) {
         return commentTestimonials.slice(0, 10); // Limit to 10 for performance
      }

      // Otherwise, combine with fallback testimonials to reach minimum of 3
      const needed = 3 - commentTestimonials.length;
      const fallbackTestimonials = testimonials.slice(0, needed);

      return [...commentTestimonials, ...fallbackTestimonials];
   } catch (error) {
      console.error("Error getting combined testimonials:", error);
      // Return fallback testimonials if there's an error
      return testimonials.slice(0, 3);
   }
}

/**
 * Get testimonials for a specific album
 */
export async function getTestimonialsForAlbum(
   albumId: string
): Promise<Testimonial[]> {
   try {
      const commentCollection = await getCollection<Comment>("comments");

      // Get approved comments with ratings for this specific album
      const comments = await commentCollection
         .find({
            albumId,
            isApproved: true,
            deletedAt: { $exists: false },
            parentId: { $exists: false }, // Only top-level comments
            rating: { $exists: true, $gte: 1 }, // Must have a rating
         })
         .sort({ createdAt: -1 })
         .toArray();

      // Transform comments to testimonials
      const testimonialPromises = comments.map((comment) =>
         transformCommentToTestimonial(comment)
      );

      const testimonialResults = await Promise.all(testimonialPromises);

      // Filter out null results and return valid testimonials
      return testimonialResults.filter(
         (testimonial): testimonial is Testimonial => testimonial !== null
      );
   } catch (error) {
      console.error("Error fetching testimonials for album:", error);
      return [];
   }
}
