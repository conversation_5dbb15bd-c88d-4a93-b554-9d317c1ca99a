"use client";

import { useQuery } from "@tanstack/react-query";
import { useAlbums } from "./use-albums";
import { useImages, useUngroupedImages } from "./use-images";
import { usePortfolioServices } from "./use-portfolio";

export interface DashboardStats {
   totalImages: number;
   totalAlbums: number;
   ungroupedImages: number;
   recentUploads: number; // This month
   totalPortfolioServices: number;
   totalPortfolioImages: number;
}

export function useDashboardStats() {
   const { data: imagesData } = useImages({ limit: 1 }); // Just get count
   const { data: albumsData } = useAlbums({ limit: 1 }); // Just get count
   const { data: ungroupedData } = useUngroupedImages({ limit: 1 }); // Just get count
   const { data: portfolioServicesData } = usePortfolioServices({ limit: 1 }); // Just get count

   return useQuery({
      queryKey: ["dashboard-stats"],
      queryFn: async (): Promise<DashboardStats> => {
         // For now, we'll use the total counts from the pagination data
         // In a real implementation, you might want separate API endpoints for stats
         const totalImages = imagesData?.pagination.total || 0;
         const totalAlbums = albumsData?.pagination.total || 0;
         const ungroupedImages = ungroupedData?.pagination.total || 0;
         const totalPortfolioServices = portfolioServicesData?.length || 0;

         // Calculate total portfolio images from services data
         const totalPortfolioImages =
            portfolioServicesData?.reduce(
               (total, service) => total + service.imageCount,
               0
            ) || 0;

         // For recent uploads, we'd need to filter by date
         // For now, we'll just use a placeholder
         const recentUploads = 0;

         return {
            totalImages,
            totalAlbums,
            ungroupedImages,
            recentUploads,
            totalPortfolioServices,
            totalPortfolioImages,
         };
      },
      enabled: !!(
         imagesData &&
         albumsData &&
         ungroupedData &&
         portfolioServicesData
      ),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}
