"use client";

import { Card, CardContent } from "@/components/ui/card";
import { testimonials } from "@/data";
import { useCombinedTestimonials } from "@/lib/hooks/use-testimonials";
import { motion } from "framer-motion";
import { Quote, <PERSON> } from "lucide-react";

export default function TestimonialSection2() {
   const { data: combinedTestimonials, isLoading } = useCombinedTestimonials();

   // Use combined testimonials if available, otherwise fallback to static testimonials
   const displayTestimonials = combinedTestimonials || testimonials;

   return (
      <section className="py-20 bg-astral-grey">
         <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
               className="text-center mb-16"
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
               viewport={{ once: true }}
            >
               <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                  Love from{" "}
                  <span className="bg-gradient-accent bg-clip-text text-transparent">
                     Our Clients
                  </span>
               </h2>
               <p className="text-lg text-muted-foreground font-montserrat max-w-2xl mx-auto">
                  Real stories from real people who trusted us with their most
                  precious moments
               </p>
            </motion.div>

            {/* Masonry Grid */}
            {isLoading ? (
               <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
                  {[1, 2, 3].map((index) => (
                     <div key={index} className="break-inside-avoid mb-6">
                        <Card className="bg-card/80 backdrop-blur-sm border-astral-grey-light p-0 animate-pulse">
                           <CardContent className="p-6">
                              <div className="flex items-center gap-4 mb-4">
                                 <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                                 <div className="space-y-2">
                                    <div className="h-4 bg-gray-300 rounded w-24"></div>
                                    <div className="h-3 bg-gray-300 rounded w-20"></div>
                                 </div>
                              </div>
                              <div className="flex items-center gap-1 mb-3">
                                 {[...Array(5)].map((_, i) => (
                                    <div
                                       key={i}
                                       className="w-4 h-4 bg-gray-300 rounded"
                                    ></div>
                                 ))}
                              </div>
                              <div className="space-y-2">
                                 <div className="h-4 bg-gray-300 rounded w-full"></div>
                                 <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                              </div>
                           </CardContent>
                        </Card>
                     </div>
                  ))}
               </div>
            ) : (
               <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
                  {displayTestimonials.map((testimonial, index) => (
                     <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ y: -5 }}
                        className="break-inside-avoid mb-6"
                     >
                        <Card className="bg-card/80 backdrop-blur-sm border-astral-grey-light hover:border-primary/30 transition-all duration-300 hover:shadow-elegant p-0">
                           <CardContent className="p-0">
                              {/* Header with profile */}
                              <div className="p-6 pb-4">
                                 <div className="flex items-center gap-4 mb-4">
                                    <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center">
                                       <span className="text-white font-playfair font-semibold">
                                          {testimonial.initials}{" "}
                                       </span>
                                    </div>
                                    <div className="flex-1">
                                       <h3 className="font-semibold text-foreground">
                                          {testimonial.name}
                                       </h3>
                                       <p className="text-sm text-primary">
                                          {testimonial.service}
                                       </p>
                                    </div>
                                    <div className="flex gap-1">
                                       {[...Array(testimonial.rating)].map(
                                          (_, i) => (
                                             <Star
                                                key={i}
                                                className="h-4 w-4 text-yellow-400 fill-yellow-400"
                                             />
                                          )
                                       )}
                                    </div>
                                 </div>
                              </div>

                              {/* Image */}
                              {/* <div className="relative aspect-[4/3] overflow-hidden">
                                 <Image
                                    src={testimonial.image}
                                    alt={`${testimonial.name} photography`}
                                    fill
                                    className="object-cover"
                                 />
                                 <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                              </div> */}

                              {/* Content */}
                              <div className="p-6">
                                 <Quote className="h-6 w-6 text-primary mb-3" />
                                 <p className="text-muted-foreground italic line-clamp-3 leading-relaxed mb-4">
                                    &quot;{testimonial.text}&quot;
                                 </p>
                              </div>
                           </CardContent>
                        </Card>
                     </motion.div>
                  ))}
               </div>
            )}

            {/* Stats Section */}
            {/* <motion.div
               className="mt-16 text-center"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.4 }}
               viewport={{ once: true }}
            >
               <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                  <div className="text-center">
                     <div className="text-3xl font-bold text-primary mb-2">
                        500+
                     </div>
                     <div className="text-sm text-muted-foreground">
                        Happy Clients
                     </div>
                  </div>
                  <div className="text-center">
                     <div className="text-3xl font-bold text-primary mb-2">
                        5.0
                     </div>
                     <div className="text-sm text-muted-foreground">
                        Average Rating
                     </div>
                  </div>
                  <div className="text-center">
                     <div className="text-3xl font-bold text-primary mb-2">
                        98%
                     </div>
                     <div className="text-sm text-muted-foreground">
                        Satisfaction Rate
                     </div>
                  </div>
                  <div className="text-center">
                     <div className="text-3xl font-bold text-primary mb-2">
                        1000+
                     </div>
                     <div className="text-sm text-muted-foreground">
                        Photos Delivered
                     </div>
                  </div>
               </div>
            </motion.div> */}
         </div>
      </section>
   );
}
