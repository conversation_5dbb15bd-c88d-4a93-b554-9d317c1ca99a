"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
   ArchiveBoxArrowDownIcon,
   TrashIcon,
   XMarkIcon,
} from "@heroicons/react/24/solid";

interface AlbumFloatingActionBarProps {
   selectedCount: number;
   onClearSelection: () => void;
   onMoveToAlbum: () => void;
   onDelete: () => void;
}

export default function AlbumFloatingActionBar({
   selectedCount,
   onClearSelection,
   onMoveToAlbum,
   onDelete,
}: AlbumFloatingActionBarProps) {
   if (selectedCount === 0) return null;

   return (
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
         <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg shadow-lg px-4 py-3">
            <div className="flex items-center space-x-2">
               <span className="text-sm font-medium text-foreground whitespace-nowrap pr-2">
                  {selectedCount} image{selectedCount !== 1 ? "s" : ""} selected
               </span>
               <Button
                  variant="outline"
                  onClick={onClearSelection}
                  className="h-8 px-2"
               >
                  <XMarkIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Clear</span>
               </Button>

               <Button
                  variant="outline"
                  onClick={onMoveToAlbum}
                  className="h-8 px-2 sm:px-3"
               >
                  <ArchiveBoxArrowDownIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Move to Album</span>
               </Button>
               <Button
                  variant="destructive"
                  onClick={onDelete}
                  className="h-8 px-2 sm:px-3"
               >
                  <TrashIcon className="w-4 h-4" />
                  <span className="hidden md:inline">Delete</span>
               </Button>
            </div>
         </div>
      </div>
   );
}
