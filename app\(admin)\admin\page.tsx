"use client";

import CreateAlbumDialog from "@/components/admin/album/create-album-dialog";
import CreatePortfolioServiceDialog from "@/components/admin/portfolio/create-portfolio-service-dialog";
import RecentActivities from "@/components/admin/recent-activities";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import UploadImagesDialog from "@/components/ui/upload-images-dialog";
import { useDashboardStats } from "@/lib/hooks/use-dashboard-stats";
import {
   ArchiveBoxIcon,
   BookmarkSquareIcon,
   FolderPlusIcon,
   PhotoIcon,
   WrenchIcon,
} from "@heroicons/react/24/solid";
import { Upload } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function AdminHomePage() {
   const { data: stats, isLoading: statsLoading } = useDashboardStats();
   const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

   const statCards = [
      {
         title: "Total Images",
         value: stats?.totalImages,
         description: "Images in gallery",
         icon: PhotoIcon,
         href: "/admin/ungrouped",
      },
      {
         title: "Albums",
         value: stats?.totalAlbums,
         description: "Organized albums",
         icon: BookmarkSquareIcon,
         href: "/admin/albums",
      },
      {
         title: "Ungrouped",
         value: stats?.ungroupedImages,
         description: "Images without albums",
         icon: ArchiveBoxIcon,
         href: "/admin/ungrouped",
      },
      {
         title: "Portfolio Services",
         value: stats?.totalPortfolioServices,
         description: "Portfolio services",
         icon: WrenchIcon,
         href: "/admin/portfolio",
      },
      {
         title: "Portfolio Images",
         value: stats?.totalPortfolioImages,
         description: "Images in portfolio",
         icon: PhotoIcon,
         href: "/admin/portfolio",
      },
   ];

   return (
      <div className="p-6 sm:p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center space-x-4">
            <div>
               <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                  Gallery Dashboard
               </h1>
               <p className="text-muted-foreground">
                  Welcome to the Astral Studios gallery management system
               </p>
            </div>
         </div>

         {/* Stats Grid */}
         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {statCards.map((stat) => {
               const Icon = stat.icon;
               return (
                  <Link key={stat.title} href={stat.href}>
                     <Card className="border-border/50 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0">
                           <CardTitle className="text-sm font-medium text-muted-foreground">
                              {stat.title}
                           </CardTitle>
                           <Icon className="size-5.5 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                           <div className="text-2xl font-bold text-foreground pb-1">
                              {statsLoading || stat.value === undefined ? (
                                 <Skeleton className="h-8 w-12" />
                              ) : (
                                 (stat.value || 0).toLocaleString()
                              )}
                           </div>
                           <p className="text-xs text-muted-foreground">
                              {stat.description}
                           </p>
                        </CardContent>
                     </Card>
                  </Link>
               );
            })}
         </div>

         {/* Main Content Area */}
         <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <RecentActivities />

            {/* Quick Actions */}
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="text-foreground">
                     Quick Actions
                  </CardTitle>
                  <CardDescription>
                     Frequently used gallery management tools
                  </CardDescription>
               </CardHeader>
               <CardContent className="h-full">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 h-full">
                     <CreateAlbumDialog
                        trigger={
                           <Button className="h-full py-4 flex-col bg-astral-grey/50 hover:bg-astral-grey/60">
                              <FolderPlusIcon className="size-4.5" />
                              <span className="text-sm">New Album</span>
                           </Button>
                        }
                     />

                     <CreatePortfolioServiceDialog
                        trigger={
                           <Button className="h-full py-4 flex-col bg-astral-grey/50 hover:bg-astral-grey/60">
                              <FolderPlusIcon className="size-4.5" />
                              <span className="text-sm">
                                 New Portfolio Service
                              </span>
                           </Button>
                        }
                     />

                     <Link href="/admin/albums">
                        <Button className="h-full py-4 flex-col w-full bg-astral-grey/50 hover:bg-astral-grey/60">
                           <BookmarkSquareIcon className="size-4.5" />
                           <span className="text-sm">View Albums</span>
                        </Button>
                     </Link>

                     <Link href="/admin/ungrouped">
                        <Button className="h-full py-4 flex-col w-full bg-astral-grey/50 hover:bg-astral-grey/60">
                           <ArchiveBoxIcon className="size-4.5" />
                           <span className="text-sm">View Ungrouped</span>
                        </Button>
                     </Link>

                     <Link href="/admin/portfolio">
                        <Button className="h-full py-4 flex-col w-full bg-astral-grey/50 hover:bg-astral-grey/60">
                           <PhotoIcon className="size-4.5" />
                           <span className="text-sm">View Portfolio</span>
                        </Button>
                     </Link>

                     <Button
                        className="h-full py-4 flex-col bg-astral-grey/50 hover:bg-astral-grey/60"
                        onClick={() => setUploadDialogOpen(true)}
                     >
                        <Upload className="size-4.5" />
                        <span className="text-sm">Upload Images</span>
                     </Button>
                  </div>
               </CardContent>
            </Card>
         </div>

         {/* Upload Dialog */}
         <UploadImagesDialog
            open={uploadDialogOpen}
            onOpenChange={setUploadDialogOpen}
            title="Upload Images"
            subtitle="Add new images to your gallery"
         />
      </div>
   );
}
