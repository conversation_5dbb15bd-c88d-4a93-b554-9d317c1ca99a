"use client";

import { MasonryGallery } from "@/components/pages/gallery";
import AlbumHero from "@/components/pages/gallery/album/album-hero";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteImages } from "@/lib/hooks/use-images";
import { AlbumWithStats } from "@/lib/models";
import { downloadImagesAsZip } from "@/lib/utils/download-utils";
import { PhotoIcon } from "@heroicons/react/24/solid";
import Lenis from "@studio-freight/lenis";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import AlbumPassword from "./album-password";

interface AlbumPageContentProps {
   album: AlbumWithStats;
}

export default function AlbumPageContent({ album }: AlbumPageContentProps) {
   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   const [password, setPassword] = useState("");
   const [isUnlocked, setIsUnlocked] = useState(!album.hasPassword);
   const [passwordError, setPasswordError] = useState("");
   const [isLiked, setIsLiked] = useState(false);

   // Infinite images (client/gallery) similar to admin
   const {
      data: imagesData,
      isLoading: imagesLoading,
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
   } = useInfiniteImages({ albumId: album._id as string, limit: 20 });

   const images = (imagesData?.pages || []).flatMap((p) => p.data);
   const totalImages =
      imagesData?.pages?.[0]?.pagination?.total ?? images.length;

   // Download all by fetching all pages lazily
   const handleDownloadAll = async () => {
      try {
         // If we already have all images, download directly
         if (images.length >= totalImages) {
            const items = images.map((img) => ({
               url: img.url,
               name: img.name || `image_${img._id}`,
            }));
            await downloadImagesAsZip(items, `${album.name}_images.zip`);
            return;
         }

         if (fetchNextPage) {
            let safety = 0;
            let next = true;
            while (next && safety < 200) {
               const res: unknown = await fetchNextPage();
               const r = res as { hasNextPage?: boolean } | undefined;
               next = !!r?.hasNextPage;
               safety += 1;
            }
         }

         const all = (imagesData?.pages || []).flatMap((p) => p.data);
         const items = all.map((img) => ({
            url: img.url,
            name: img.name || `image_${img._id}`,
         }));
         await downloadImagesAsZip(items, `${album.name}_images.zip`);
      } catch (e) {
         console.error(e);
      }
   };

   // Handle password submission
   const handlePasswordSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (password === album.password) {
         setIsUnlocked(true);
         setPasswordError("");
      } else {
         setPasswordError("Incorrect password. Please try again.");
      }
   };

   // Handle share
   const handleShare = async () => {
      const shareData = {
         title: album.name,
         text: album.description || `Check out this album: ${album.name}`,
         url: window.location.href,
      };

      if (navigator.share) {
         try {
            await navigator.share(shareData);
         } catch (error) {
            console.error("Error sharing album:", error);
            // Fallback to copying URL
            navigator.clipboard.writeText(window.location.href);
         }
      } else {
         navigator.clipboard.writeText(window.location.href);
      }
   };

   // Handle like toggle
   const handleLike = () => {
      setIsLiked(!isLiked);
      // Here you would typically make an API call to save the like
   };

   // Password protection dialog
   if (album.hasPassword && !isUnlocked) {
      return (
         <AlbumPassword
            album={album}
            handlePasswordSubmit={handlePasswordSubmit}
            password={password}
            setPassword={setPassword}
            passwordError={passwordError}
         />
      );
   }

   return (
      <div className="min-h-screen relative">
         <AlbumHero
            album={album}
            handleLike={handleLike}
            handleShare={handleShare}
            isLiked={isLiked}
            images={images.map((image) => ({
               url: image.url,
               filename: image.name,
            }))}
            onDownloadAll={handleDownloadAll}
         />

         {/* Images Gallery */}
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="container mx-auto px-6 py-16"
         >
            <div className="mb-8">
               <h2 className="text-3xl font-bold text-foreground mb-3">
                  Album Gallery
               </h2>
               <p className="text-muted-foreground mb-2">
                  Click on any image to view it in full size in an interactive
                  lightbox.
               </p>
               <p className="text-muted-foreground text-sm">
                  {totalImages} photos
               </p>
            </div>

            {imagesLoading ? (
               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, i) => (
                     <Skeleton key={i} className="aspect-[4/5] rounded-xl" />
                  ))}
               </div>
            ) : images.length > 0 ? (
               <MasonryGallery
                  images={images}
                  columns={3}
                  gap={22}
                  enableLightbox={true}
                  showLoadingStates={true}
                  hasNextPage={hasNextPage}
                  isFetchingNextPage={isFetchingNextPage}
                  fetchNextPage={fetchNextPage}
               />
            ) : (
               <div className="text-center py-20">
                  <PhotoIcon className="size-20 mx-auto mb-6 text-muted-foreground/50" />
                  <h3 className="text-2xl font-semibold text-foreground mb-4">
                     No Images Found
                  </h3>
                  <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                     This album doesn&apos;t contain any images yet. Check back
                     later for updates.
                  </p>
                  <Button asChild variant="outline">
                     <Link href="/gallery/albums">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Albums
                     </Link>
                  </Button>
               </div>
            )}
         </motion.div>
      </div>
   );
}
